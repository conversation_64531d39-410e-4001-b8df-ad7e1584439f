import api from './api'

export interface Store {
  id: number
  name: string
  email: string
  address: string
  owner_id: number
  averageRating?: number
  totalRatings?: number
  createdAt?: string
  updatedAt?: string
}

export interface CreateStoreData {
  name: string
  email: string
  address: string
  owner_id?: number
}

export interface StoreFilters {
  name?: string
  address?: string
}

export interface StoreWithRatings extends Store {
  ratings: Array<{
    id: number
    rating: number
    user: {
      id: number
      name: string
      email: string
    }
    createdAt: string
  }>
}

class StoreService {
  async getAllStores(filters?: StoreFilters): Promise<Store[]> {
    const params = new URLSearchParams()
    
    if (filters?.name) params.append('name', filters.name)
    if (filters?.address) params.append('address', filters.address)

    const response = await api.get(`/stores?${params.toString()}`)
    return response.data
  }

  async getStoreById(id: number): Promise<StoreWithRatings> {
    const response = await api.get(`/stores/${id}`)
    return response.data
  }

  async createStore(storeData: CreateStoreData): Promise<Store> {
    const response = await api.post('/stores', storeData)
    return response.data
  }

  async getMyStore(): Promise<Store> {
    const response = await api.get('/stores/my-store')
    return response.data
  }

  async getStoreRatings(storeId: number) {
    const response = await api.get(`/stores/${storeId}/ratings`)
    return response.data
  }

  async updateStore(id: number, storeData: Partial<Store>): Promise<Store> {
    const response = await api.patch(`/stores/${id}`, storeData)
    return response.data
  }
}

export default new StoreService()
