import api from './api'

export interface Rating {
  id: number
  rating: number
  user_id: number
  store_id: number
  createdAt: string
  updatedAt: string
  user?: {
    id: number
    name: string
    email: string
  }
  store?: {
    id: number
    name: string
    address: string
  }
}

export interface CreateRatingData {
  rating: number
  store_id: number
}

export interface UpdateRatingData {
  rating: number
}

class RatingsService {
  async createRating(ratingData: CreateRatingData): Promise<Rating> {
    const response = await api.post('/ratings', ratingData)
    return response.data
  }

  async updateRating(id: number, ratingData: UpdateRatingData): Promise<Rating> {
    const response = await api.patch(`/ratings/${id}`, ratingData)
    return response.data
  }

  async getAllRatings(): Promise<Rating[]> {
    const response = await api.get('/ratings')
    return response.data
  }

  async getMyRatings(): Promise<Rating[]> {
    const response = await api.get('/ratings/my-ratings')
    return response.data
  }

  async getRatingsByStore(storeId: number): Promise<Rating[]> {
    const response = await api.get(`/ratings/store/${storeId}`)
    return response.data
  }

  async getUserRatingForStore(storeId: number): Promise<Rating | null> {
    try {
      const myRatings = await this.getMyRatings()
      return myRatings.find(rating => rating.store_id === storeId) || null
    } catch (error) {
      console.error('Error fetching user rating for store:', error)
      return null
    }
  }
}

export default new RatingsService()
