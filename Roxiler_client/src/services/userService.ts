import api from './api'

export interface User {
  id: number
  name: string
  email: string
  address: string
  role: string
  createdAt?: string
  updatedAt?: string
}

export interface CreateUserData {
  name: string
  email: string
  address: string
  password: string
  role: string
}

export interface UpdatePasswordData {
  password: string
}

export interface UserFilters {
  name?: string
  email?: string
  address?: string
  role?: string
}

class UserService {
  async getAllUsers(filters?: UserFilters): Promise<User[]> {
    const params = new URLSearchParams()
    
    if (filters?.name) params.append('name', filters.name)
    if (filters?.email) params.append('email', filters.email)
    if (filters?.address) params.append('address', filters.address)
    if (filters?.role) params.append('role', filters.role)

    const response = await api.get(`/users?${params.toString()}`)
    return response.data
  }

  async getUserById(id: number): Promise<User> {
    const response = await api.get(`/users/${id}`)
    return response.data
  }

  async createUser(userData: CreateUserData): Promise<User> {
    const response = await api.post('/users', userData)
    return response.data
  }

  async updatePassword(id: number, passwordData: UpdatePasswordData): Promise<void> {
    await api.patch(`/users/${id}/password`, passwordData)
  }

  async getMyProfile(): Promise<User> {
    const userId = localStorage.getItem('userId')
    if (!userId) {
      throw new Error('User not authenticated')
    }
    return this.getUserById(parseInt(userId))
  }

  async updateMyProfile(userData: Partial<User>): Promise<User> {
    const userId = localStorage.getItem('userId')
    if (!userId) {
      throw new Error('User not authenticated')
    }
    const response = await api.patch(`/users/${userId}`, userData)
    return response.data
  }
}

export default new UserService()
