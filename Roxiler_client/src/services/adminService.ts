import api from './api'
import { CreateUserData } from './userService'
import { CreateStoreData } from './storeService'

export interface DashboardStats {
  totalUsers: number
  totalStores: number
  totalRatings: number
}

export interface CreateStoreOwnerData {
  user: CreateUserData
  store: CreateStoreData
}

class AdminService {
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await api.get('/admin/dashboard')
    return response.data
  }

  async createUser(userData: CreateUserData) {
    const response = await api.post('/admin/users', userData)
    return response.data
  }

  async createStore(storeData: CreateStoreData) {
    const response = await api.post('/admin/stores', storeData)
    return response.data
  }

  async createStoreOwner(data: CreateStoreOwnerData) {
    const response = await api.post('/admin/store-owners', data)
    return response.data
  }
}

export default new AdminService()
