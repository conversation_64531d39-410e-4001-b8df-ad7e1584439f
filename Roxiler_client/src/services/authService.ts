import api from './api'

export interface LoginData {
  email: string
  password: string
}

export interface RegisterData {
  name: string
  email: string
  address: string
  password: string
}

export interface AuthResponse {
  access_token: string
  user: {
    id: number
    name: string
    email: string
    role: string
    address?: string
  }
}

class AuthService {
  async login(data: LoginData): Promise<AuthResponse> {
    const response = await api.post('/auth/login', data)
    
    // Store auth data in localStorage
    if (response.data.access_token) {
      localStorage.setItem('authToken', response.data.access_token)
      localStorage.setItem('userRole', response.data.user.role)
      localStorage.setItem('userId', response.data.user.id.toString())
      localStorage.setItem('userName', response.data.user.name)
      localStorage.setItem('userEmail', response.data.user.email)
    }
    
    return response.data
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await api.post('/auth/register', data)
    return response.data
  }

  logout(): void {
    localStorage.removeItem('authToken')
    localStorage.removeItem('userRole')
    localStorage.removeItem('userId')
    localStorage.removeItem('userName')
    localStorage.removeItem('userEmail')
  }

  getCurrentUser() {
    const token = localStorage.getItem('authToken')
    const role = localStorage.getItem('userRole')
    const userId = localStorage.getItem('userId')
    const userName = localStorage.getItem('userName')
    const userEmail = localStorage.getItem('userEmail')

    if (!token || !role || !userId) {
      return null
    }

    return {
      id: parseInt(userId),
      name: userName,
      email: userEmail,
      role,
      token
    }
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('authToken')
  }

  getUserRole(): string | null {
    return localStorage.getItem('userRole')
  }
}

export default new AuthService()
