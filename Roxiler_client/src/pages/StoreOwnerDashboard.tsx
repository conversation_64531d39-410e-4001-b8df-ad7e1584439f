import { useState, useEffect } from 'react'
import { Routes, Route, Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import storeService, { type Store } from '../services/storeService'
import ratingsService, { type Rating } from '../services/ratingsService'

const StoreOwnerDashboard = () => {
  const navigate = useNavigate()
  const { logout } = useAuth()
  const [store, setStore] = useState<Store | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStoreData = async () => {
      try {
        const storeData = await storeService.getMyStore()
        setStore(storeData)
      } catch (error) {
        console.error('Error fetching store data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStoreData()
  }, [])

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading store data...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-6 py-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Store Owner Dashboard</h1>
            <p className="text-gray-600">{store?.name || 'Store'}</p>
          </div>
          <button
            onClick={handleLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm"
          >
            Logout
          </button>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <ul className="space-y-2">
              <li>
                <Link
                  to="/store"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  Dashboard
                </Link>
              </li>
              <li>
                <Link
                  to="/store/ratings"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  Customer Ratings
                </Link>
              </li>
              <li>
                <Link
                  to="/store/profile"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  Store Profile
                </Link>
              </li>
            </ul>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <Routes>
            <Route path="/" element={<StoreDashboardHome store={store} />} />
            <Route path="/ratings" element={<CustomerRatings />} />
            <Route path="/profile" element={<StoreProfile store={store} />} />
          </Routes>
        </main>
      </div>
    </div>
  )
}

// Store Dashboard Home Component
const StoreDashboardHome = ({ store }: { store: Store | null }) => {
  const [ratings, setRatings] = useState<Rating[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchRatings = async () => {
      if (store) {
        try {
          const ratingsData = await ratingsService.getRatingsByStore(store.id)
          setRatings(ratingsData)
        } catch (error) {
          console.error('Error fetching ratings:', error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchRatings()
  }, [store])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading dashboard...</div>
      </div>
    )
  }

  if (!store) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Store not found</div>
      </div>
    )
  }

  const ratingDistribution = [1, 2, 3, 4, 5].map(rating => ({
    rating,
    count: ratings.filter(r => r.rating === rating).length
  }))

  const recentRatings = ratings.slice(0, 3)

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Dashboard Overview</h2>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900">Average Rating</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{store.averageRating || 0}/5</p>
          <p className="text-sm text-gray-500 mt-1">⭐⭐⭐⭐⭐</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900">Total Ratings</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{ratings.length}</p>
          <p className="text-sm text-gray-500 mt-1">Customer reviews</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
          <p className="text-3xl font-bold text-purple-600 mt-2">{recentRatings.length}</p>
          <p className="text-sm text-gray-500 mt-1">New ratings today</p>
        </div>
      </div>

      {/* Rating Distribution */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Rating Distribution</h3>
        <div className="space-y-3">
          {ratingDistribution.reverse().map(({ rating, count }) => (
            <div key={rating} className="flex items-center">
              <span className="w-12 text-sm text-gray-600">{rating} ⭐</span>
              <div className="flex-1 mx-4 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${(count / ratings.length) * 100}%` }}
                ></div>
              </div>
              <span className="w-12 text-sm text-gray-600 text-right">{count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Ratings */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Customer Ratings</h3>
        <div className="space-y-4">
          {recentRatings.map((rating) => (
            <div key={rating.id} className="border-b border-gray-200 pb-4 last:border-b-0">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="font-medium text-gray-900">{rating.user?.name || 'Anonymous'}</p>
                  <p className="text-sm text-gray-500">{rating.user?.email || 'No email'}</p>
                </div>
                <div className="text-right">
                  <div className="flex items-center">
                    <span className="text-yellow-400">{'⭐'.repeat(rating.rating)}</span>
                    <span className="ml-1 text-sm text-gray-600">({rating.rating}/5)</span>
                  </div>
                  <p className="text-xs text-gray-500">{new Date(rating.createdAt).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 text-center">
          <Link 
            to="/store/ratings"
            className="text-blue-600 hover:text-blue-500 text-sm font-medium"
          >
            View All Ratings →
          </Link>
        </div>
      </div>
    </div>
  )
}

// Customer Ratings Component
const CustomerRatings = () => {
  const [ratings, setRatings] = useState<Rating[]>([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState<'date' | 'rating'>('date')
  const [filterRating, setFilterRating] = useState<number | null>(null)

  useEffect(() => {
    const fetchRatings = async () => {
      try {
        const store = await storeService.getMyStore()
        const ratingsData = await ratingsService.getRatingsByStore(store.id)
        setRatings(ratingsData)
      } catch (error) {
        console.error('Error fetching ratings:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRatings()
  }, [])

  const filteredAndSortedRatings = ratings
    .filter(rating => filterRating === null || rating.rating === filterRating)
    .sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      } else {
        return b.rating - a.rating
      }
    })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading ratings...</div>
      </div>
    )
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Customer Ratings</h2>
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort by:
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'date' | 'rating')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            >
              <option value="date">Date</option>
              <option value="rating">Rating</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Filter by rating:
            </label>
            <select
              value={filterRating || ''}
              onChange={(e) => setFilterRating(e.target.value ? Number(e.target.value) : null)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            >
              <option value="">All ratings</option>
              <option value="5">5 stars</option>
              <option value="4">4 stars</option>
              <option value="3">3 stars</option>
              <option value="2">2 stars</option>
              <option value="1">1 star</option>
            </select>
          </div>
        </div>
      </div>

      {/* Ratings List */}
      <div className="bg-white rounded-lg shadow">
        <div className="divide-y divide-gray-200">
          {filteredAndSortedRatings.map((rating) => (
            <div key={rating.id} className="p-6">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-medium text-gray-900">{rating.user?.name || 'Anonymous'}</h3>
                  <p className="text-sm text-gray-500">{rating.user?.email || 'No email'}</p>
                </div>
                <div className="text-right">
                  <div className="flex items-center">
                    <span className="text-yellow-400">{'⭐'.repeat(rating.rating)}</span>
                    <span className="ml-1 text-sm text-gray-600">({rating.rating}/5)</span>
                  </div>
                  <p className="text-xs text-gray-500">{new Date(rating.createdAt).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Store Profile Component
const StoreProfile = ({ store }: { store: Store | null }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: store?.name || '',
    email: store?.email || '',
    address: store?.address || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  useEffect(() => {
    if (store) {
      setFormData({
        name: store.name,
        email: store.email,
        address: store.address,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }
  }, [store])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
      alert('New passwords do not match!')
      return
    }

    try {
      if (store) {
        // Update store information
        await storeService.updateStore(store.id, {
          name: formData.name,
          email: formData.email,
          address: formData.address
        })

        // Update password if provided (this would need a separate endpoint)
        if (formData.newPassword) {
          // Note: This would need to be implemented in the backend
          console.log('Password update not implemented yet')
        }

        alert('Store profile updated successfully!')
        setIsEditing(false)
        setFormData(prev => ({ ...prev, currentPassword: '', newPassword: '', confirmPassword: '' }))
      }
    } catch (error) {
      console.error('Error updating store profile:', error)
      alert('Failed to update store profile. Please try again.')
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  if (!store) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Store not found</div>
      </div>
    )
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Store Profile</h2>

      <div className="bg-white rounded-lg shadow p-6 max-w-2xl">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">Store Information</h3>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
          >
            {isEditing ? 'Cancel' : 'Edit Profile'}
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Store Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              disabled={!isEditing}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none disabled:bg-gray-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              disabled={!isEditing}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none disabled:bg-gray-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <textarea
              name="address"
              value={formData.address}
              onChange={handleChange}
              disabled={!isEditing}
              rows={3}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none disabled:bg-gray-50"
            />
          </div>

          {isEditing && (
            <>
              <div className="border-t pt-4">
                <h4 className="text-md font-medium text-gray-900 mb-4">Change Password</h4>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Current Password
                    </label>
                    <input
                      type="password"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      New Password
                    </label>
                    <input
                      type="password"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="8-16 characters with uppercase and special character"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    />
                  </div>
                </div>
              </div>

              <button
                type="submit"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg"
              >
                Save Changes
              </button>
            </>
          )}
        </form>

        {/* Store Statistics */}
        <div className="mt-8 pt-6 border-t">
          <h4 className="text-md font-medium text-gray-900 mb-4">Store Statistics</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">Average Rating</p>
              <p className="text-2xl font-bold text-blue-600">{store?.averageRating || 0}/5</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold text-green-600">{store?.totalRatings || 0}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreOwnerDashboard
