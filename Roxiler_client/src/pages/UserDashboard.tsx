import { useState, useEffect } from 'react'
import { Routes, Route, Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import storeService, { type Store } from '../services/storeService'
import ratingsService, { type Rating } from '../services/ratingsService'
import userService, { type User } from '../services/userService'

const UserDashboard = () => {
  const navigate = useNavigate()
  const { logout } = useAuth()

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-6 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">User Dashboard</h1>
          <button
            onClick={handleLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm"
          >
            Logout
          </button>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <ul className="space-y-2">
              <li>
                <Link
                  to="/user"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  Browse Stores
                </Link>
              </li>
              <li>
                <Link
                  to="/user/profile"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  My Profile
                </Link>
              </li>
            </ul>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <Routes>
            <Route path="/" element={<StoresList />} />
            <Route path="/profile" element={<UserProfile />} />
          </Routes>
        </main>
      </div>
    </div>
  )
}

// Stores List Component for Users
const StoresList = () => {
  const [stores, setStores] = useState<Store[]>([])
  const [myRatings, setMyRatings] = useState<Rating[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [storesData, ratingsData] = await Promise.all([
          storeService.getAllStores(),
          ratingsService.getMyRatings()
        ])
        setStores(storesData)
        setMyRatings(ratingsData)
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const filteredStores = stores.filter(store =>
    store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    store.address.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleRatingSubmit = async (storeId: number, rating: number) => {
    try {
      const existingRating = myRatings.find(r => r.store_id === storeId)

      if (existingRating) {
        await ratingsService.updateRating(existingRating.id, { rating })
        setMyRatings(prev => prev.map(r =>
          r.id === existingRating.id ? { ...r, rating } : r
        ))
      } else {
        const newRating = await ratingsService.createRating({ store_id: storeId, rating })
        setMyRatings(prev => [...prev, newRating])
      }

      alert(`Rating ${rating} submitted successfully!`)
    } catch (error) {
      console.error('Error submitting rating:', error)
      alert('Failed to submit rating. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading stores...</div>
      </div>
    )
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Browse Stores</h2>
      
      <div className="mb-6">
        <input
          type="text"
          placeholder="Search stores by name or address..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredStores.map((store) => {
          const userRating = myRatings.find(r => r.store_id === store.id)
          return (
            <StoreCard
              key={store.id}
              store={store}
              userRating={userRating?.rating || null}
              onRatingSubmit={(rating) => handleRatingSubmit(store.id, rating)}
            />
          )
        })}
      </div>
    </div>
  )
}

// Store Card Component
const StoreCard = ({ store, userRating, onRatingSubmit }: {
  store: Store,
  userRating: number | null,
  onRatingSubmit: (rating: number) => void
}) => {
  const [selectedRating, setSelectedRating] = useState<number>(userRating || 0)

  useEffect(() => {
    setSelectedRating(userRating || 0)
  }, [userRating])

  const handleSubmitRating = () => {
    if (selectedRating > 0) {
      onRatingSubmit(selectedRating)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{store.name}</h3>
      <p className="text-gray-600 text-sm mb-3">{store.address}</p>

      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Overall Rating:</span>
          <span className="font-medium">{store.averageRating ? `${store.averageRating}/5` : 'No ratings'} ⭐</span>
        </div>
        
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Your Rating:</span>
          <span className="font-medium">
            {userRating ? `${userRating}/5 ⭐` : 'Not rated'}
          </span>
        </div>
      </div>

      <div className="border-t pt-4">
        <p className="text-sm text-gray-600 mb-2">
          {userRating ? 'Update your rating:' : 'Rate this store:'}
        </p>
        
        <div className="flex items-center space-x-2 mb-3">
          {[1, 2, 3, 4, 5].map((rating) => (
            <button
              key={rating}
              onClick={() => setSelectedRating(rating)}
              className={`w-8 h-8 rounded-full border-2 text-sm font-medium ${
                selectedRating >= rating
                  ? 'bg-yellow-400 border-yellow-400 text-white'
                  : 'border-gray-300 text-gray-400 hover:border-yellow-400'
              }`}
            >
              {rating}
            </button>
          ))}
        </div>
        
        <button
          onClick={handleSubmitRating}
          disabled={selectedRating === 0}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-2 rounded-lg text-sm"
        >
          {userRating ? 'Update Rating' : 'Submit Rating'}
        </button>
      </div>
    </div>
  )
}

// User Profile Component
const UserProfile = () => {
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<User | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    address: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const userData = await userService.getMyProfile()
        setUser(userData)
        setFormData({
          name: userData.name,
          email: userData.email,
          address: userData.address,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        })
      } catch (error) {
        console.error('Error fetching user profile:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
      alert('New passwords do not match!')
      return
    }

    try {
      // Update profile information
      if (user) {
        await userService.updateMyProfile({
          name: formData.name,
          email: formData.email,
          address: formData.address
        })

        // Update password if provided
        if (formData.newPassword) {
          await userService.updatePassword(user.id, { password: formData.newPassword })
        }

        alert('Profile updated successfully!')
        setIsEditing(false)
        setFormData(prev => ({ ...prev, currentPassword: '', newPassword: '', confirmPassword: '' }))

        // Refresh user data
        const updatedUser = await userService.getMyProfile()
        setUser(updatedUser)
        setFormData(prev => ({
          ...prev,
          name: updatedUser.name,
          email: updatedUser.email,
          address: updatedUser.address
        }))
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Failed to update profile. Please try again.')
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading profile...</div>
      </div>
    )
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">My Profile</h2>
      
      <div className="bg-white rounded-lg shadow p-6 max-w-2xl">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">Profile Information</h3>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
          >
            {isEditing ? 'Cancel' : 'Edit Profile'}
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              disabled={!isEditing}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none disabled:bg-gray-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              disabled={!isEditing}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none disabled:bg-gray-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <textarea
              name="address"
              value={formData.address}
              onChange={handleChange}
              disabled={!isEditing}
              rows={3}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none disabled:bg-gray-50"
            />
          </div>

          {isEditing && (
            <>
              <div className="border-t pt-4">
                <h4 className="text-md font-medium text-gray-900 mb-4">Change Password</h4>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Current Password
                    </label>
                    <input
                      type="password"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      New Password
                    </label>
                    <input
                      type="password"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="8-16 characters with uppercase and special character"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    />
                  </div>
                </div>
              </div>

              <button 
                type="submit"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg"
              >
                Save Changes
              </button>
            </>
          )}
        </form>
      </div>
    </div>
  )
}

export default UserDashboard
